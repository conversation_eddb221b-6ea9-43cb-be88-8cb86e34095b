{"theme": {"title": "Paramètres du thème", "subtitle": "Choisissez votre thème d'application préféré", "light": "<PERSON>", "dark": "Mode Sombre", "system": "Par défaut du système", "lightDescription": "Thème clair classique pour une meilleure visibilité en plein jour", "darkDescription": "Reposant pour les yeux, parfait pour une utilisation nocturne", "systemDescription": "Correspond automatiquement au thème de votre appareil", "note": "Remarque: Les changements de thème s'appliqueront immédiatement"}, "language": {"title": "Paramètres de langue", "subtitle": "Choisissez votre langue préférée", "note": "Les modifications seront appliquées immédiatement", "system": "Par défaut du système"}, "emailSupport": {"title": "Comment pouvons-nous vous aider?", "subtitle": "Nous sommes là pour vous aider avec toutes vos questions ou préoccupations. Veuillez choisir la meilleure façon de nous contacter:", "contactForm": {"title": "Formulaire de contact", "description": "Remplissez notre formulaire de contact pour une réponse rapide."}, "emailUs": {"title": "Envoyez-nous un email", "description": "Envoyez-nous un email directement pour des demandes détaillées."}}, "callSupport": {"title": "Contactez-nous", "subtitle": "Appelez-nous au numéro ci-dessous. Nous sommes disponibles du lundi au vendredi, de 9h à 17h."}, "loginIssue": {"title": "Problèmes de connexion?", "subtitle": "Si vous rencontrez des difficultés pour vous connecter, voici quelques problèmes courants et étapes de dépannage:", "commonIssues": "Problèmes courants", "troubleshootingSteps": "Étapes de dépannage", "issues": {"incorrectEmail": {"title": "Email incorrect", "description": "Assurez-vous d'utiliser l'adresse email correcte associée à votre compte."}, "incorrectPassword": {"title": "Mot de passe incorrect", "description": "Vérifiez votre mot de passe pour les fautes de frappe ou les majuscules incorrectes."}, "forgotPassword": {"title": "Mot de passe oublié", "description": "Si vous avez oublié votre mot de passe, utilisez l'option 'Mot de passe oublié' pour le réinitialiser."}}, "steps": {"clearCache": {"title": "Effacer le cache de l'application", "description": "Effacez le cache et les données de l'application dans les paramètres de votre appareil."}, "reinstall": {"title": "Réinstaller l'application", "description": "Désinstallez et réinstallez l'application depuis le magasin d'applications de votre appareil."}, "updateOS": {"title": "Mettre à jour le système d'exploitation", "description": "Assurez-vous que le système d'exploitation de votre appareil est à jour."}}}, "help": {"sections": {"authentication": "Authentification", "contactUs": "Contactez-nous", "settings": "Paramètres"}, "options": {"loginIssues": "Problèmes de connexion", "signupIssues": "Problèmes d'inscription", "passwordRecovery": "Récupération de mot de passe", "emailSupport": "Support par email", "callSupport": "Support téléphonique", "language": "<PERSON><PERSON>", "theme": "Thème"}}, "layouts": {"help": "Aide", "callSupport": "Support téléphonique", "emailSupport": "Support par email", "loginIssue": "Problèmes de connexion", "passwordRecovery": "Récupération de mot de passe", "signupIssue": "Problèmes d'inscription", "language": "<PERSON><PERSON>", "theme": "Thème"}, "signupHelp": {"title": "Problème d'inscription ?", "intro": "Si vous avez des difficultés à vous inscrire, voici quelques problèmes courants et étapes de dépannage :", "commonIssuesTitle": "Problèmes courants", "invalidEmailTitle": "Adresse e-mail invalide", "invalidEmailDesc": "Assurez-vous que votre adresse e-mail est correctement saisie et qu'elle n'est pas déjà associée à un compte existant.", "passwordReqTitle": "Exigences du mot de passe", "passwordReqDesc": "Assurez-vous que votre mot de passe respecte les critères requis (par exemple, longueur, caractères spéciaux).", "incorrectPasswordTitle": "Mot de passe incorrect", "incorrectPasswordDesc": "Assurez-vous d'utiliser l'adresse e-mail correcte associée à votre compte.", "existingAccountTitle": "Compte existant", "existingAccountDesc": "Si vous vous êtes déjà inscrit, essayez de vous connecter ou utilisez l'option 'Mot de passe oublié'.", "troubleshootingTitle": "Étapes de dépannage", "verifyCredsTitle": "Vérifier les identifiants", "verifyCredsDesc": "Vérifiez soigneusement votre adresse e-mail et votre mot de passe pour détecter toute faute de frappe ou erreur.", "socialMediaTitle": "Inscription via les réseaux sociaux", "socialMediaDesc": "Si vous utilisez un compte de réseau social pour vous inscrire, assurez-vous qu'il est correctement lié et autorisé.", "differentDeviceTitle": "Essayer un autre appareil", "differentDeviceDesc": "Essayez de vous inscrire sur un autre appareil ou navigateur pour écarter les problèmes spécifiques à un appareil.", "contactSupportTitle": "<PERSON>er le support", "contactSupportDesc": "<PERSON> le problème persiste, contactez notre équipe d'assistance pour obtenir de l'aide."}, "authentication": {"title": "Authentification", "accountCreation": {"title": "Création de compte", "howToCreate": "Comment créer un compte ?", "createAccountDescription": "Pour créer un compte, appuyez sur 'S'inscrire' sur l'écran d'accueil et suivez les instructions. Vous devrez fournir votre adresse e-mail et créer un mot de passe sécurisé.", "incorrectEmail": "E-mail incorrect", "incorrectEmailDescription": "Assurez-vous d'utiliser l'adresse e-mail correcte associée à votre compte.", "troubleCreatingAccount": "J'ai des difficultés à créer un compte.", "troubleCreatingAccountDescription": "Si vous rencontrez des problèmes lors de la création du compte, assurez-vous que votre adresse e-mail est valide et n'est pas déjà utilisée. Vérifiez les messages d'erreur et suivez les instructions fournies."}, "login": {"title": "Connexion", "howToLogin": "Comment me connecter ?", "loginDescription": "Pour vous connecter, appuyez sur 'Se connecter' sur l'écran d'accueil et entrez votre adresse e-mail enregistrée et votre mot de passe. Si vous avez oublié votre mot de passe, appuyez sur 'Mot de passe oublié' pour le réinitialiser.", "troubleLoggingIn": "J'ai des difficultés à me connecter.", "troubleLoggingInDescription": "Si vous avez des difficultés à vous connecter, vérifiez soigneusement votre adresse e-mail et votre mot de passe. Si vous avez oublié votre mot de passe, utilisez l'option 'Mot de passe oublié' pour le réinitialiser.", "resetPasswordQuestion": "Comment réinitialiser mon mot de passe ?", "resetPasswordDescription": "Pour réinitialiser votre mot de passe, appuyez sur 'Mot de passe oublié' sur l'écran de connexion. Entrez votre adresse e-mail, et nous vous enverrons un lien pour réinitialiser votre mot de passe. Suivez les instructions dans l'e-mail pour créer un nouveau mot de passe.", "didNotReceiveEmail": "Je n'ai pas reçu l'e-mail de réinitialisation du mot de passe.", "didNotReceiveEmailDescription": "Si vous ne recevez pas l'e-mail de réinitialisation, vérifiez votre dossier spam ou courrier indésirable. Assurez-vous d'avoir saisi la bonne adresse e-mail associée à votre compte. Si vous ne l'avez toujours pas reçu, contactez notre équipe d'assistance pour obtenir de l'aide."}}, "homepage": {"discoverLocalTreasures": "Découvrez des trésors locaux", "connectWithLocalArtisans": "Connectez-vous avec des artisans et marques locales, et trouvez des objets uniques dans votre communauté.", "logIn": "Se connecter", "signUp": "S'inscrire", "byContinuingAgree": "En continuant, vous acceptez nos", "termsOfService": "Conditions d'utilisation et", "privacyPolicy": "Politique de confidentialité"}, "onboarding": {"slide": {"title": "Découvrez les marques locales", "description": "<PERSON><PERSON>vez facilement des magasins locaux uniques autour de vous."}, "slide-two": {"title": "Explorez les produits", "description": "Parcourez une grande variété de produits authentiques."}, "slide-three": {"title": "Profitez d'un shopping fluide", "description": "Expérience fluide sur mobile et web."}, "getStarted": "Commencer", "next": "Suivant"}, "two": {"accountTypePrompt": "Choisissez votre type de compte"}, "User": "Utilisa<PERSON>ur", "auth": {"userTypes": {"client": "Utilisa<PERSON>ur", "vendor": "<PERSON><PERSON><PERSON>"}, "forgotPassword": {"email": {"label": "Email", "placeholder": "Entrez votre adresse e-mail"}, "resetButton": "Réinitialiser le mot de passe", "title": "Mot de passe oublié", "description": "Saisissez l'adresse e-mail associée à votre compte et nous vous enverrons les instructions pour réinitialiser votre mot de passe."}, "verifyEmail": {"title": "Vérifiez votre adresse e-mail", "description_parts": {"before": "Nous avons envoyé un e-mail de vérification à", "after": "Veuillez consulter votre boîte de réception pour valider votre compte."}}, "resetPassword": {"title": "Réinitialisez votre mot de passe", "description_parts": {"before": "Un OTP de réinitialisation a été envoyé à", "after": "Veuillez vérifier votre boîte de réception."}, "enterOtp": "Entrez le code reçu sur votre téléphone", "enterOtpEmail": "Entrez le code reçu sur votre email", "changePassword": "Changez votre mot de passe", "theOTP": "Le code OTP est composé de 4 chiffres", "noOtp": "Vous n'avez pas reçu de code OTP ?", "resend": "<PERSON><PERSON><PERSON>", "password": {"label": "Mot de passe", "placeholder": "Entrez votre mot de passe"}, "confirmPassword": {"label": "Confirmez le mot de passe", "placeholder": "Confirmez votre mot de passe"}, "submit": "Confirmer"}, "register": {"title": "<PERSON><PERSON><PERSON> un compte", "label": {"placeholder": "Nom complet"}, "email": {"placeholder": "Entrez votre email"}, "password": {"label": "Mot de passe", "placeholder": "Entrez votre mot de passe"}, "confirmPassword": {"label": "Confirmer le mot de passe", "placeholder": "Ressaisissez votre mot de passe"}, "registerButton": "S'inscrire", "haveAccount": "Vous avez déjà un compte ?", "login": "Se connecter"}, "login": {"title": "Connexion", "email": {"placeholder": "Entrez votre email"}, "password": {"placeholder": "Entrez votre mot de passe"}, "loginButton": "Se connecter", "noAccount": "Vous n'avez pas de compte ?", "signUp": "<PERSON><PERSON><PERSON> un compte", "forgetPasswordLink": "Mot de passe oublié ?"}}, "validation": {"required": {"otp": "Le code OTP est requis", "email": "L'email est requis", "password": "Le mot de passe est requis", "confirmPassword": "La confirmation du mot de passe est requise", "fullName": "Le nom complet est requis", "phone": "Le numéro de téléphone est requis", "code": "Le code est requis", "room": "La salle est requise", "message": "Le message est requis", "comment": "Le commentaire est requis", "rating": "La note est requise", "brandName": "Le nom de la marque est requis", "productName": "Le nom du produit est requis", "description": "La description est requise", "logo": "Le logo est requis", "address": "L'adresse est requise", "latitude": "La latitude est requise", "longitude": "La longitude est requise", "city": "La ville est requise", "state": "L'état est requis", "country": "Le pays est requis", "zipCode": "Le code postal est requis", "location": "La localisation est requise", "category": "La catégorie est requise", "brand": "La marque est requise", "price": "Le prix est requis", "images": "Les images sont requises"}, "email": {"invalid": "Veu<PERSON>z entrer une adresse email valide"}, "phone": {"invalid": "Veuillez entrer un numéro de téléphone valide"}, "fullName": {"min": "Le nom complet doit contenir au moins 2 caractères", "max": "Le nom complet ne doit pas dépasser 50 caractères"}, "room": {"min": "Le nom de la salle doit contenir au moins 3 caractères"}, "message": {"min": "Le message ne peut pas être vide", "max": "Le message ne doit pas dépasser 500 caractères"}, "comment": {"min": "Le commentaire ne peut pas être vide", "max": "Le commentaire ne doit pas dépasser 500 caractères"}, "rating": {"min": "La note doit être au moins 1", "max": "La note doit être au maximum 5"}, "brandName": {"min": "Le nom de la marque doit contenir au moins 2 caractères"}, "productName": {"min": "Le nom du produit doit contenir au moins 2 caractères"}, "description": {"min": "La description doit contenir au moins 10 caractères"}, "address": {"min": "L'adresse doit contenir au moins 5 caractères"}, "city": {"min": "La ville doit contenir au moins 2 caractères"}, "state": {"min": "L'état doit contenir au moins 2 caractères"}, "country": {"min": "Le pays doit contenir au moins 2 caractères"}, "zipCode": {"min": "Le code postal doit contenir au moins 3 caractères"}, "category": {"min": "La catégorie doit contenir au moins 2 caractères"}, "price": {"typeError": "Le prix doit être un nombre", "min": "Le prix ne peut pas être négatif"}, "images": {"min": "Au moins une image est requise", "max": "Maximum de six images autorisées"}, "min": "{{field}} doit contenir au moins {{min}} caractères", "max": "{{field}} ne doit pas dépasser {{max}} caractères", "matches": "{{field}} doit correspondre à {{match}}", "code": {"required": "Le code est requis", "length": "Le code doit comporter exactement 4 chiffres"}, "password": {"min": "Le mot de passe doit contenir au moins 8 caractères", "matches": "Les mots de passe doivent correspondre", "requirements": "Le mot de passe doit contenir une majuscule, une minuscule, un chiffre et un caractère spécial", "strength": {"label": "Force :", "weak": " Faible", "fair": " <PERSON><PERSON><PERSON>", "good": " <PERSON>", "strong": " Fort"}}, "otp": {"required": "Le code OTP est requis", "length": "Le code OTP doit contenir exactement 4 chiffres"}, "location": {"required": "La localisation est requise"}}, "error": {"notFound": {"title": "Cet écran n'existe pas.", "message": "<PERSON><PERSON><PERSON> à la page principale."}, "generic": {"title": "Oups ! Quelque chose s'est mal passé.", "message": "<PERSON><PERSON><PERSON> chose s'est mal passé.", "retry": "<PERSON><PERSON><PERSON><PERSON>"}, "network": {"title": "Erreur de Connexion", "message": "Veuillez vérifier votre connexion internet et réessayer.", "retry": "<PERSON><PERSON><PERSON><PERSON>"}}, "backend": {"account_type_mismatch": "Type de compte non correspondant", "invalid_account_type": "Type de compte invalide", "cannot_delete_vendor_with_active_orders": "Impossible de supprimer un vendeur avec des commandes actives", "vendor_not_found": "Vendeur non trouvé", "yourAccountDeleted": "Votre compte a été supprimé avec succès", "bad_request": "<PERSON><PERSON><PERSON><PERSON><PERSON> invalide", "internal_server_error": "<PERSON>rreur interne du serveur", "validation_error": "Erreur de validation", "unexpected_field_detectted_in_request_body": "Champ inattendu détecté dans le corps de la requête", "unexpected_field_detected": "Champ inattendu détecté", "email_required": "L'email est requis", "email_invalid": "L'email est invalide", "password_required": "Le mot de passe est requis", "password_min_length": "Le mot de passe doit contenir au moins 8 caractères", "name_required": "Le nom est requis", "name_min_length": "Le nom doit contenir au moins 3 caractères", "name_max_length": "Le nom doit contenir au maximum 50 caractères", "type_required": "Le type est requis", "type_invalid": "Le type doit être 'client' ou 'vendeur'", "otp_required": "Le code OTP est requis", "otp_invalid_length": "Le code OTP doit contenir exactement 4 caractères", "otp_type_required": "Le type de code OTP est requis", "otp_type_invalid": "Le type de code OTP doit être 'reset-password' ou 'created-account'", "new_password_required": "Le nouveau mot de passe est requis", "new_password_min_length": "Le nouveau mot de passe doit contenir au moins 8 caractères", "userNotFound": "Utilisateur non trouvé", "loginSuccess": "Connexion réussie", "invalidCredentials": "Identifiants invalides", "blockedLogin": "<PERSON><PERSON><PERSON><PERSON>", "accountNotVerified": "Le compte n'est pas vérifié", "emailNotExist": "L'email n'existe pas", "emailExists": "L'email existe déjà", "registrationSuccess": "Inscription réussie", "otpEmailFailed": "Échec de l'envoi de l'email OTP", "serverError": "<PERSON><PERSON><PERSON> du <PERSON>", "brand_name_required": "Le nom de la marque est requis", "brand_description_must_be_string": "La description de la marque doit être une chaîne", "brand_email_invalid": "<PERSON><PERSON> de la marque invalide", "brand_phone_must_be_string": "Le téléphone de la marque doit être une chaîne", "location_address_required": "L'adresse de localisation est requise", "location_latitude_must_be_number": "La latitude de localisation doit être un nombre", "location_longitude_must_be_number": "La longitude de localisation doit être un nombre", "location_city_required": "La ville de localisation est requise", "location_state_required": "L'état de localisation est requis", "location_country_required": "Le pays de localisation est requis", "location_zipcode_required": "Le code postal de localisation est requis", "location_must_be_object": "La localisation doit être un objet", "location_required": "La localisation est requise", "brand_category_required": "La catégorie de la marque est requise", "product_name_required": "Le nom du produit est requis", "product_description_must_be_string": "La description du produit doit être une chaîne", "product_price_required": "Le prix du produit est requis", "product_price_must_be_positive": "Le prix du produit doit être positif", "product_category_required": "La catégorie du produit est requise", "product_category_invalid": "Catégorie de produit invalide", "product_brand_invalid": "Marque de produit invalide", "product_images_must_be_array": "Les images de produit doivent être un tableau", "order_status_required": "Le statut de commande est requis", "order_status_invalid": "Statut de commande invalide", "access_token_missing_or_invalid": "Token d'accès manquant ou invalide", "invalid_or_expired_token": "Token invalide ou expiré", "brand_created": "<PERSON><PERSON>", "failed_to_create_brand": "Échec de la création de marque", "brand_not_found": "Marque non trouvée", "brand_found": "<PERSON><PERSON> trou<PERSON>", "failed_to_fetch_brand": "Échec de la récupération de marque", "brand_updated": "<PERSON><PERSON> mise à jour", "failed_to_update_brand": "Échec de la mise à jour de marque", "brands_found": "<PERSON><PERSON> trouvées", "failed_to_fetch_brands": "Échec de la récupération de marques", "product_created": "Produit c<PERSON>", "failed_to_create_product": "Échec de la création de produit", "product_not_found": "Produit non trouvé", "product_found": "Produit trouvé", "failed_to_fetch_product": "Échec de la récupération de produit", "product_updated": "Produit mis à jour", "failed_to_update_product": "Échec de la mise à jour de produit", "products_found": "Produits trouvés", "failed_to_fetch_products": "Échec de la récupération de produits", "invalid_brand": "<PERSON><PERSON> invalide", "categories_found": "Catégories trouvées", "failed_to_fetch_categories": "Échec de la récupération de catégories", "orders_found": "Commandes trouvées", "failed_to_fetch_orders": "Échec de la récupération de commandes", "order_found": "Commande trouvée", "failed_to_fetch_order": "Échec de la récupération de commande", "order_status_updated": "Statut de commande mis à jour", "failed_to_update_order_status": "Échec de la mise à jour du statut de commande", "failed_to_fetch_dashboard_analytics": "Échec de la récupération d'analytiques du tableau de bord", "item_added_to_wishlist": "Article ajouté à la liste de souhaits", "failed_to_add_to_wishlist": "Échec de l'ajout à la liste de souhaits", "client_not_found": "Client non trouvé", "item_removed_from_wishlist": "Article supprimé de la liste de souhaits", "failed_to_remove_from_wishlist": "Échec de la suppression de la liste de souhaits", "location_added": "Localisation ajoutée", "failed_to_add_location": "Échec de l'ajout de localisation", "locations_found": "Localisations trouvées", "failed_to_fetch_locations": "Échec de la récupération de localisations", "location_updated": "Localisation mise à jour", "failed_to_update_location": "Échec de la mise à jour de localisation", "location_deleted": "Localisation supprimée", "failed_to_delete_location": "Échec de la suppression de localisation", "notification_not_found": "Notification non trouvée", "notification_marked_as_read": "Notification marquée comme lue", "failed_to_mark_notification": "Échec du marquage de notification", "notifications_found": "Notifications trouvées", "failed_to_fetch_notifications": "Échec de la récupération de notifications", "review_added": "<PERSON><PERSON> a<PERSON>", "failed_to_add_review": "Échec de l'ajout d'avis", "reviews_found": "<PERSON><PERSON> trou<PERSON>", "failed_to_fetch_reviews": "Échec de la récupération d'avis", "search_failed": "Échec de la recherche", "search_results_found": "Résultats de recherche trouvés", "order_products_required": "Les produits de commande sont requis", "brand_required_for_order": "La marque est requise pour la commande", "location_required_for_order": "La localisation est requise pour la commande", "invalid_product_in_order": "Produit invalide dans la commande", "total_price_mismatch": "Incohérence du prix total", "order_created": "Commande cré<PERSON>", "failed_to_create_order": "Échec de la création de commande", "order_not_found": "Commande non trouvée", "order_cannot_be_cancelled": "La commande ne peut pas être annulée", "order_cancelled": "Commande annulée", "failed_to_cancel_order": "Échec de l'annulation de commande", "invalid_service_id": "ID de service invalide", "profile_not_found": "Profil non trouvé", "service_retrieved_successfully": "Service récupéré avec succès", "get_profile_failed": "Échec de la récupération de profil", "expo_push_token_not_found": "Token push Expo non trouvé", "no_expo_push_tokens_found": "Aucun token push Expo trouvé", "expo_push_tokens_updated": "Tokens push Expo mis à jour", "error_updating_expo_push_tokens": "Erreur lors de la mise à jour des tokens push Expo", "server_error": "<PERSON><PERSON><PERSON> du <PERSON>", "new_password_and_confirm_password_do_not_match": "Le nouveau mot de passe et la confirmation ne correspondent pas", "old_password_is_required": "L'ancien mot de passe est requis", "user_not_found": "Utilisateur non trouvé", "old_password_is_incorrect": "L'ancien mot de passe est incorrect", "new_password_must_be_different_from_old_password": "Le nouveau mot de passe doit être différent de l'ancien", "failed_to_change_password": "Échec du changement de mot de passe", "client_or_profile_not_found": "Client ou profil non trouvé", "account_deleted_successfully": "Compte supprimé avec succès", "password_changed": "Mot de passe modifié", "invalid_type_must_be_hotel_client_or_admin": "Type invalide doit être hôtel, client ou admin", "expo_push_token_updated": "Token push Expo mis à jour", "expo_push_token_already_exists": "Le token push Expo existe déjà", "expo_push_token_added": "Token push Expo ajouté", "passwordChanged": "Mot de passe modifié", "noData": "<PERSON><PERSON><PERSON> donn<PERSON> trouvée", "invalidOtp": "OTP invalide ou expiré", "invalidOtpSimple": "OTP invalide", "invalidOtpType": "Type d'OTP invalide", "pleaseWaitSeconds": "Veuillez attendre quelques secondes avant de demander un nouvel OTP", "unexpectedError": "Une erreur inattendue s'est produite", "otp_sent": "Un OTP a été envoyé à votre email", "success": "Su<PERSON>ès", "password_reset": "Mot de passe réinitialisé avec succès", "otp_verified": "OTP vérifié avec succès", "error": "<PERSON><PERSON><PERSON>", "authentication_required": "Authentification requise", "insufficient_permissions": "Permissions insuffisantes", "account_blocked": "Votre compte est bloqué", "internal_server_error_during_authorization": "Erreur interne du serveur lors de l'autorisation", "brandAddedSuccessfully": "<PERSON><PERSON> avec succès", "brandChangedSuccessfully": "<PERSON><PERSON> modifi<PERSON> avec succès"}, "settings": {"title": "Paramètres", "subtitle": "<PERSON><PERSON><PERSON> votre compte et vos préférences", "profile": {"title": "Profil", "form": {"fullName": {"label": "Nom Co<PERSON>t", "placeholder": "Entrez votre nom complet"}, "email": {"label": "Email", "placeholder": "Entrez votre email"}, "phone": {"label": "Téléphone", "placeholder": "Entrez votre téléphone"}}}, "sections": {"account": {"title": "<PERSON><PERSON><PERSON>", "profile": {"title": "Paramètres du Profil", "subtitle": "Mettez à jour vos informations personnelles", "form": {"fullName": {"label": "Nom Co<PERSON>t", "placeholder": "Entrez votre nom complet"}, "email": {"label": "Email", "placeholder": "Entrez votre email"}, "phone": {"label": "Téléphone", "placeholder": "Entrez votre téléphone"}, "address": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Entrez votre adresse"}}, "saveButton": "Enregistrer le Profil"}, "notifications": {"title": "Notifications", "subtitle": "<PERSON><PERSON><PERSON> vos préférences de notification"}, "security": {"title": "Sécurité", "subtitle": "Changez votre mot de passe et vos paramètres de sécurité"}, "orders": {"title": "Commandes", "subtitle": "Consultez votre historique de commandes"}, "locations": {"title": "Emplacements", "subtitle": "<PERSON><PERSON><PERSON> vos emplacements enregistrés"}}, "app": {"title": "Paramètres de l'Application", "language": {"title": "<PERSON><PERSON>", "subtitle": "Changer la langue de l'application"}, "theme": {"title": "Thème", "subtitle": "Actuellement : {{mode}}"}}, "support": {"title": "Support", "help": {"title": "Centre d'Aide", "subtitle": "Obtenez de l'aide et contactez le support"}, "about": {"title": "À propos", "subtitle": "Version de l'application et informations"}}}, "security": {"title": "Paramètres de Sécurité", "changePassword": {"title": "Changer <PERSON> de Passe", "currentPassword": {"label": "Mot de Passe Actuel", "placeholder": "Entrez le mot de passe actuel"}, "newPassword": {"label": "Nouveau Mot de Passe", "placeholder": "Entrez le nouveau mot de passe"}, "confirmPassword": {"label": "Confirmez le Nouveau Mot de Passe", "placeholder": "Confirmez le nouveau mot de passe"}, "button": "Changer <PERSON> de Passe", "errors": {"mismatch": "Les nouveaux mots de passe ne correspondent pas", "length": "Le mot de passe doit comporter au moins 8 caractères"}}, "twoFactor": {"comming": "Cette fonctionnalité arrive bientôt", "title": "Authentification à Deux Facteurs", "description": "A<PERSON><PERSON>z une couche de sécurité supplémentaire à votre compte en activant l'authentification à deux facteurs.", "button": "Configurer l'authentification à deux facteurs"}}}, "common": {"deleteAccount": "Supprimer le compte", "logout": "Déconnexion", "save": "Enregistrer", "saving": "Enregistrement...", "delete": {"title": "Supprimer le compte", "message": "Êtes-vous sûr de vouloir supprimer cet compte ?", "cancelButton": "Annuler", "deleteButton": "<PERSON><PERSON><PERSON><PERSON>"}}, "screens": {"headers": {"about": "À propos", "help": "Aide", "language": "<PERSON><PERSON>", "login-history": "Historique de connexion", "notifications": "Notifications", "profile": "Profil", "security": "Sécurité", "theme": "Thème", "notification": "Notification", "brands": "Marques", "products": "Produits", "brand": "Marque", "product": "Produit", "order-details": "<PERSON><PERSON><PERSON> de la Commande", "brand-details": "<PERSON>é<PERSON> de la Marque", "product-details": "Détails du Produit", "add-brand": "A<PERSON>ter une Marque", "add-product": "Ajouter un Produit", "edit-brand": "Modifier la Marque", "edit-product": "Modifier le Produit", "products-brand": "Produits de la Marque", "orders": "Commandes", "locations": "Emplacements"}}, "helpSettings": {"title": "Centre d'Aide", "subtitle": "Comment pouvons-nous vous aider aujou<PERSON>'hui ?", "categories": {"support": {"title": "<PERSON><PERSON> le <PERSON>", "description": "Contactez notre équipe de support"}, "documentation": {"title": "Documentation", "description": "Lisez notre documentation détaillée"}, "tutorials": {"title": "Tutoriels Vid<PERSON>o", "description": "Regardez des vidéos tutoriels utiles"}}, "faq": {"title": "Questions Fréquemment Po<PERSON>ées", "items": {"hotelListings": {"question": "Comment puis-je trouver des marques et artisans locaux ?", "answer": "Vous pouvez parcourir les marques et artisans locaux en utilisant notre fonction de recherche, en explorant les catégories, ou en consultant les marques en vedette sur la page d'accueil. Assurez-vous que les services de localisation sont activés pour voir les entreprises près de chez vous."}, "complaints": {"question": "Comment puis-je signaler un problème ou faire une réclamation ?", "answer": "Si vous rencontrez des problèmes avec des commandes, des produits ou des services, vous pouvez contacter notre équipe de support directement via l'option Contacter le Support dans le Centre d'Aide. Nous répondrons à votre demande dès que possible."}, "orders": {"question": "Comment puis-je suivre mes commandes ?", "answer": "Vous pouvez suivre vos commandes en allant dans la section Commandes dans les paramètres de votre compte. Vous y verrez tout votre historique de commandes avec les mises à jour de statut actuelles, y compris les commandes en attente, acceptées, livrées ou annulées."}, "account": {"question": "Comment puis-je gérer les paramètres de mon compte ?", "answer": "Vous pouvez gérer votre compte en allant dans Paramètres depuis le menu principal. Vous pouvez y mettre à jour votre profil, changer les préférences de langue et de thème, gérer les notifications, consulter l'historique des commandes et accéder aux paramètres de sécurité."}, "services": {"question": "Quels services propose Locasa ?", "answer": "Locasa est une plateforme de marché local qui vous connecte avec des artisans et des marques locales. Vous pouvez découvrir des produits uniques, passer des commandes, gérer vos favoris, suivre les livraisons et soutenir les entreprises locales de votre communauté."}}}}, "about": {"version": "Version {{version}} ({{buildNumber}})", "title": "À Propos de Nous", "description": "Locasa est une plateforme de marché local complète conçue pour connecter les utilisateurs avec des artisans et marques locales. Notre plateforme offre des outils puissants pour découvrir des produits uniques et soutenir les entreprises locales de votre communauté.", "connect": "Connectez-vous avec Nous", "social": {"website": "Site Web", "twitter": "Twitter", "instagram": "Instagram"}, "legal": {"title": "Légal", "privacyPolicy": "Politique de Confidentialité", "termsOfService": "Conditions d'Utilisation", "licenses": "Licences"}, "copyright": "© {{year}} Locasa. Tous droits réservés."}, "notifications": {"title": "Paramètres des notifications", "pushNotifications": "Notifications Push", "emailNotifications": "Notifications par Email", "bookingUpdates": "Mises à jour de Réservation", "newMessages": "Nouveaux Messages", "marketing": "Marketing", "pushNotificationsDescription": "Recevez des notifications push pour les mises à jour importantes", "emailNotificationsDescription": "Recevez des notifications par email pour les activités de votre compte", "bookingUpdatesDescription": "Recevez des notifications pour les changements de statut de réservation", "newMessagesDescription": "Recevez des notifications pour les nouveaux messages", "marketingDescription": "Recevez des offres promotionnelles et des mises à jour", "permissionRequired": "Autorisation Requise", "permissionRequiredMessage": "Veuillez activer les autorisations de notification dans les paramètres de votre appareil pour recevoir les notifications push et les alertes de nouveaux messages.", "empty": {"title": "Vous êtes à jour !", "description": "Vous n'avez aucune notification pour le moment. Revenez plus tard ou explorez quelques marques locales !"}, "messages": {"recommendation": "🎯 Nouveau produit recommandé juste pour vous !", "invitation": "🏷️ Vous êtes invité à explorer une nouvelle marque !", "accepted": "📦 Votre commande a été expédiée. Attendez l'appel de livraison !", "favorite_brand": "✨ Découvrez ce nouvel article de votre marque préférée !"}}, "navigation": {"tabs": {"home": "Accueil", "favorites": "<PERSON><PERSON><PERSON>", "shopc": "<PERSON><PERSON>", "settings": "Paramètres", "orders": "Commandes", "brands": "Marques", "products": "Produits", "dashboard": "Analytique"}}, "dashboard": {"overview": "<PERSON><PERSON><PERSON><PERSON>", "totalOrders": "Total des Commandes", "delivered": "Livrées", "pending": "En Attente", "monthlyOrders": "Commandes Mensu<PERSON>", "totalOrdersOverTime": "Total des Commandes dans le Temps", "orderStatusDistribution": "Répartition du Statut des Commandes", "deliveredOrders": "Commandes Livrées", "successfullyDeliveredThisPeriod": "Livrées avec Succès Cette Période", "mostOrderedProducts": "Produits les Plus Commandés", "bestPerformingProducts": "Produits les Plus Performants", "orderStatistics": "Statistiques des Commandes", "completedOrders": "Commandes Terminées", "cancelledOrders": "Commandes Annulées", "productPerformance": "Performance des Produits", "customerEngagement": "Engagement Client", "activeCustomers": "Clients Actifs", "newCustomers": "Nouveaux Clients", "topProducts": "Top 5"}, "notFound": {"title": "Oups ! Page non trouvée", "description": "La page que vous recherchez a peut-être été supprimée, son nom a changé, ou elle est temporairement indisponible.", "goHome": "Retour à l'accueil"}, "app": {"name": "Locasa"}, "ui": {"emptyBrand": {"title": "Aucune marque pour le moment", "description": "Commencez par ajouter votre première marque pour raconter votre histoire et vendre vos produits !", "noFavorites": "Aucune marque trouvée. Veuillez vérifier cette section plus tard pour ajouter une nouvelle marque à vos favoris."}}, "location": {"permission": {"title": "Activer la localisation", "description": "Pour trouver des marques et artisans locaux près de chez vous, Locasa a besoin d'accéder à votre localisation. Veuillez activer les services de localisation dans les paramètres de votre appareil.", "openSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres"}}, "buttons": {"help": "Aide", "addNewBrand": "Ajouter une nouvelle marque", "enableLocationAccess": "Activer l'accès à la localisation", "saveBrand": "Enregistrer la marque", "saveProduct": "Enregistrer le produit", "updateProduct": "Mettre à jour le produit", "cancel": "Annuler", "addToCart": "A<PERSON>ter au panier", "addToFavorites": "Ajouter aux favoris", "removeFromFavorites": "Retirer des favoris", "startShopping": "Commencer les achats", "viewProducts": "Voir les produits", "addReview": "Ajouter un avis", "viewAll": "Voir tout", "updateBrand": "Mettre à jour la marque", "openSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres"}, "search": {"placeholder": "Rechercher des marques et produits", "recentlySearched": "Recherches récentes", "sampleSearches": ["Pizza", "<PERSON><PERSON>", "Burgers", "Pasta"], "result": "résultat", "results": "résultats", "found": "trouvé", "noResults": "Aucun résultat trouvé", "tryDifferentKeywords": "Essayez de rechercher avec des mots-clés différents"}, "modal": {"deleteConfirmation": {"defaultTitle": "Confirmation de suppression", "defaultMessage": "Êtes-vous sûr de vouloir supprimer cet élément ? Cette action ne peut pas être annulée.", "defaultCancel": "Annuler", "defaultDelete": "<PERSON><PERSON><PERSON><PERSON>"}}, "timestamps": {"minsAgo": "Il y a 2 min", "hourAgo": "Il y a 1 heure", "today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er"}, "emptyStates": {"orders": {"title": "Aucune commande pour le moment", "description": "Aucun ordre trouvé. Veuillez vérifier cette section plus tard."}, "products": {"title": "Aucun produit pour le moment", "description": "Aucun produit trouvé. Veuillez vérifier cette section plus tard."}, "reviews": {"title": "Aucun avis pour le moment", "description": "Soyez le premier à partager votre expérience avec Crafted Goods. Vos commentaires aident les autres à découvrir des produits locaux de qualité."}, "locations": {"title": "Aucun emplacement pour le moment", "description": "Ajoutez votre premier emplacement pour gérer vos adresses enregistrées."}}, "orderStatus": {"pending": "En attente", "accepted": "Expédié", "delivered": "Livré", "cancelled": "<PERSON><PERSON><PERSON>"}, "errors": {"orderNotFound": "Commande non trouvée", "generic": {"title": "Oups ! Quelque chose s'est mal passé.", "retry": "<PERSON><PERSON><PERSON><PERSON>"}}, "orderDetails": {"sections": {"product": "Produit", "products": "Produits", "customer": "Client", "delivery": "<PERSON><PERSON><PERSON>", "orderInformation": "Informations de Commande"}, "labels": {"brand": "Marque", "phone": "Téléphone", "customerId": "ID Client", "address": "<PERSON><PERSON><PERSON>", "locationType": "Type d'Emplacement", "orderDate": "Date de Commande", "orderStatus": "Statut de Commande", "totalPrice": "Prix Total", "orderId": "ID de Commande", "unitPrice": "Prix Unitaire", "totalForItem": "Total pour l'Article"}, "fallbacks": {"unknownProduct": "Produit Inconnu", "unknownBrand": "<PERSON><PERSON>", "unknownCustomer": "Client Inconnu", "phoneNotProvided": "Téléphone Non Fourni", "addressNotProvided": "<PERSON><PERSON><PERSON>", "notAvailable": "Non Disponible"}, "actions": {"markAsDelivered": "<PERSON><PERSON>", "markAsAccepted": "Accepter", "markAsCancelled": "Annuler", "returnToPending": "Retourner en Attente", "updateStatus": "Mettre à Jour le Statut"}, "modal": {"cancelTitle": "Annuler la Commande", "cancelMessage": "Êtes-vous sûr de vouloir annuler cette commande ? Cette action ne peut pas être annulée et la commande ne peut pas être remise en attente.", "cancelConfirm": "Annuler la Commande", "cancelCancel": "Garder la Commande", "deliveredTitle": "Marquer comme Livré", "deliveredMessage": "Êtes-vous sûr de vouloir marquer cette commande comme livrée ? Cette action notifiera le client que sa commande a été complétée.", "deliveredConfirm": "Marquer comme Livré", "deliveredCancel": "Annuler"}, "status": {"orderDelivered": "Commande Livrée"}}, "cart": {"title": "<PERSON><PERSON>", "item": "article", "items": "articles", "viewCart": "Voir le Panier", "clearAll": "<PERSON><PERSON>", "subtotal": "Sous-total", "shipping": "<PERSON><PERSON><PERSON>", "total": "Total", "free": "<PERSON><PERSON><PERSON>", "viewFullCart": "Voir le Panier Complet", "labels": {"subtotal": "Sous-total", "shipping": "<PERSON><PERSON><PERSON>", "total": "Total", "free": "<PERSON><PERSON><PERSON>"}, "actions": {"checkout": "Commander", "processing": "Traitement en cours..."}, "brandValidation": {"title": "<PERSON><PERSON> Di<PERSON>rente Détectée", "description": "Vous ne pouvez ajouter que des produits d'une seule marque à la fois. Votre panier contient actuellement des articles d'une marque différente.", "currentBrand": "<PERSON><PERSON>", "newBrand": "Nouvelle Marque", "suggestion": "Vous pouvez soit vider votre panier et ajouter ce produit, soit continuer vos achats avec votre marque actuelle.", "clearAndAdd": "Vider le Panier et Ajouter", "keepShopping": "Garder le Panier Actuel"}, "orderCreated": "Commande créée avec succès !"}, "addresses": {"title": "<PERSON><PERSON>", "editLocation": "Modifier l'Emplacement", "selectType": "Sélectionner le Type d'Adresse", "types": {"home": "<PERSON><PERSON><PERSON>", "work": "Travail", "other": "<PERSON><PERSON>", "workplace": "Bureau"}, "empty": {"title": "<PERSON><PERSON>ne adresse enregistrée", "description": "A<PERSON><PERSON>z une adresse pour rendre la commande plus rapide et plus facile."}, "actions": {"addAddress": "A<PERSON>ter une Adresse", "addNewAddress": "A<PERSON>ter une Nouvelle Adresse"}}, "orders": {"title": "Vos Commandes", "multipleProducts": "{{count}} Produits", "actions": {"cancel": "Annuler la Commande"}, "modal": {"cancelTitle": "Annuler la Commande", "cancelMessage": "Êtes-vous sûr de vouloir annuler cette commande ? Cette action ne peut pas être annulée.", "cancelConfirm": "Annuler la Commande", "cancelCancel": "Garder la Commande"}}, "locations": {"title": "Emplacements", "subtitle": "<PERSON><PERSON><PERSON> vos emplacements enregistrés", "empty": {"title": "Aucun emplacement enregistré", "description": "Ajoutez votre premier emplacement pour gérer vos adresses."}, "actions": {"edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "addLocation": "Ajouter un Emplacement"}, "labels": {"address": "<PERSON><PERSON><PERSON>", "city": "Ville", "state": "État", "country": "Pays", "zipCode": "Code Postal", "type": "Type"}}, "map": {"selectLocation": "Sélectionner cet Emplacement", "updateLocation": "Mettre à Jour l'Emplacement", "searchPlaceholder": "Rechercher un emplacement", "selectedLocation": "Emplacement Sélectionné", "selectedAddress": "<PERSON><PERSON><PERSON>", "confirmLocation": "Confirmer l'Emplacement", "errors": {"noLocationTitle": "Emplacement Non Trouvé", "noLocationMessage": "Impossible de déterminer votre emplacement actuel"}}, "addressType": {"title": "Sélectionner le Type d'Adresse", "selectedLocation": "Emplacement Sélectionné", "saveAddress": "Enregistrer l'Adresse", "descriptions": {"home": "Votre adresse de domicile", "work": "Votre adresse de travail", "other": "<PERSON>tre adresse"}}, "share": {"title": "Partager"}, "alerts": {"success": "Su<PERSON>ès", "error": "<PERSON><PERSON><PERSON>", "orderMarkedDelivery": "Commande marquée comme livrée", "deleteProductFailed": "Échec de la suppression du produit", "deleteOrderFailed": "Échec de la suppression de la commande", "cancelOrderFailed": "Échec de l'annulation de la commande", "pushNotificationDevice": "Notification push envoyée à l'appareil"}, "vendor": {"headers": {"products": "Produits", "productsSubtitle": "G<PERSON>rez votre catalogue de produits", "welcomeBack": "Bon retour", "brandsSubtitle": "Gérez vos marques et produits", "orders": "Commandes", "ordersSubtitle": "<PERSON><PERSON><PERSON> les commandes clients"}, "labels": {"yourBrands": "Vos marques", "products": "produits", "noBrand": "Aucune marque", "category": "<PERSON><PERSON><PERSON><PERSON>", "notSpecified": "Non spécifié"}, "errors": {"somethingWentWrong": "<PERSON><PERSON><PERSON> chose s'est mal passé"}, "productDetails": {"labels": {"price": "Prix", "unnamedProduct": "Produit sans nom", "brand": "Marque", "details": "Détails", "category": "<PERSON><PERSON><PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON>", "updated": "Mis à jour", "unknown": "Inconnu"}, "actions": {"edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "modal": {"title": "Supprimer le produit", "message": "Êtes-vous sûr de vouloir supprimer ce produit ? Cette action ne peut pas être annulée.", "confirmText": "<PERSON><PERSON><PERSON><PERSON>", "cancelText": "Annuler"}, "errors": {"noProductFound": "Aucun produit trouvé"}}, "productForm": {"placeholders": {"productName": "Nom du produit *", "productDescription": "Description du produit", "selectCategory": "Sélectionner une catégorie *", "price": "Prix *"}}, "brandDetails": {"actions": {"editBrand": "Modifier la marque", "deleteBrand": "Supprimer la marque"}, "sections": {"about": "À propos", "contact": "Contact", "location": "Emplacement", "products": "Produits", "reviews": "<PERSON><PERSON>"}, "labels": {"email": "E-mail", "phone": "Téléphone", "products": "produits", "reviews": "avis", "noDescription": "Aucune description disponible pour cette marque.", "brandLocation": "Emplacement de la marque", "noCommentProvided": "Aucun commentaire fourni"}, "modal": {"title": "Supprimer la marque", "message": "Cette action supprimera définitivement la marque et toutes ses données associées. Cela ne peut pas être annulé.", "confirmText": "<PERSON><PERSON><PERSON><PERSON>", "cancelText": "Annuler"}}}, "client": {"shop": {"shopByCategory": "Acheter par catégorie"}, "sliders": {"featuredBrands": "Marques en vedette", "featuredProducts": "Produits en vedette", "viewAll": "Voir tout"}, "favorites": {"brands": "<PERSON>ques <PERSON>s", "products": "Produits Favoris"}, "featured": {"label": "<PERSON>", "empty": "Aucun élément en vedette disponible"}, "cart": {"empty": {"title": "Votre panier est vide", "description": "Parcourez notre collection sélectionnée de marques et artisans locaux pour trouver quelque chose que vous aimez."}}, "products": {"title": "Produits", "titleWithCount": "Produits ({{count}})"}, "brands": {"title": "Marques", "titleWithCount": "<PERSON><PERSON> ({{count}})"}, "brandDetails": {"vendor": "<PERSON><PERSON><PERSON>", "about": "À propos", "reviews": "<PERSON><PERSON>", "noDescription": "Aucune description disponible", "noDetailedDescription": "Aucune description détaillée disponible pour cette marque.", "unknownVendor": "Vendeur inconnu", "locationNotSpecified": "Emplacement non spécifié", "noCommentProvided": "Aucun commentaire fourni"}}, "permissions": {"gallery": {"title": "Activer l'accès aux photos", "description": "Pour télécharger des photos, Locasa a besoin d'accéder à votre photothèque. Veuillez l'activer dans les paramètres de votre appareil."}, "notifications": {"title": "Restez informé", "description": "Activez les notifications pour recevoir des mises à jour sur vos commandes, nouveaux produits et offres exclusives.", "openSettings": "<PERSON><PERSON><PERSON><PERSON>r les Paramètres", "notNow": "Pas maintenant"}}, "bottomSheets": {"addReview": {"title": "Ajouter un avis", "rating": "Note", "comment": "Commentaire", "submit": "So<PERSON><PERSON><PERSON>"}, "login": {"title": "Rôle Requis", "description": "Veuillez vous connecter pour continuer", "loginButton": "Se connecter", "cancelButton": "Annuler"}, "brands": {"title": "Sélectionner une Marque", "searchPlaceholder": "Rechercher des marques..."}, "categories": {"title": "Sélectionner une Catégorie", "searchPlaceholder": "Rechercher des catégories..."}}}